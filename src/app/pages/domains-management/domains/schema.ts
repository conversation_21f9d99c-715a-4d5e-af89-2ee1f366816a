import { SchemaFilterMatchEnum, SwuiGridField } from '@skywind-group/lib-swui';

const STATUS__CLASS_LIST = {
  active: 'sw-chip sw-chip-green',
  unavailable: 'sw-chip sw-chip-gray',
};

const MONITORING_STATUS_CLASS_LIST = {
  AVAILABLE: 'sw-chip sw-chip-green',
  BLOCKED: 'sw-chip sw-chip-red',
  UNKNOWN: 'sw-chip sw-chip-gray',
};

export const DOMAIN_NAME_SCHEMA: SwuiGridField =
{
  field: 'domain',
  title: 'DOMAINS.GRID.domain',
  type: 'string',
  isList: true,
  isViewable: false,
  isSortable: false,
  isFilterable: true,
  filterMatch: SchemaFilterMatchEnum.Contains
};

export const DOMAIN_SCHEMA: SwuiGridField[] = [
  {
    field: 'status',
    title: 'DOMAINS.GRID.status',
    type: 'select',
    isList: true,
    data: [
      { id: 'active', code: 'active', displayName: 'DOMAINS.GRID.statusActive' },
      { id: 'suspended', code: 'suspended', displayName: 'DOMAINS.GRID.statusInactive' }
    ],
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: (row: any, schema: SwuiGridField) => row[schema.field],
      classFn: (row: any, schema: SwuiGridField) => {
        return STATUS__CLASS_LIST[row[schema.field]] || 'sw-chip sw-chip-blue';
      }
    },
  },
  {
    field: 'info.monitoringStatus',
    title: 'DOMAINS.GRID.monitoringStatus',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: (row: any) => {
        const monitoringStatus = row.info?.monitoringStatus;
        if (!monitoringStatus) {
          return 'N/A';
        }

        const adapters = Object.keys(monitoringStatus);
        if (adapters.length === 0) {
          return 'N/A';
        }

        // Show status for each adapter
        return adapters.map(adapter => {
          const status = monitoringStatus[adapter];
          return `${adapter}: ${status.accessStatus}`;
        }).join(', ');
      },
      classFn: (row: any) => {
        const monitoringStatus = row.info?.monitoringStatus;
        if (!monitoringStatus) {
          return MONITORING_STATUS_CLASS_LIST.UNKNOWN;
        }

        const adapters = Object.keys(monitoringStatus);
        if (adapters.length === 0) {
          return MONITORING_STATUS_CLASS_LIST.UNKNOWN;
        }

        // If any adapter shows BLOCKED, show red
        const hasBlocked = adapters.some(adapter =>
          monitoringStatus[adapter].accessStatus === 'BLOCKED'
        );
        if (hasBlocked) {
          return MONITORING_STATUS_CLASS_LIST.BLOCKED;
        }

        // If all adapters show AVAILABLE, show green
        const allAvailable = adapters.every(adapter =>
          monitoringStatus[adapter].accessStatus === 'AVAILABLE'
        );
        if (allAvailable) {
          return MONITORING_STATUS_CLASS_LIST.AVAILABLE;
        }

        return MONITORING_STATUS_CLASS_LIST.UNKNOWN;
      }
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },
  {
    field: 'description',
    title: 'DOMAINS.GRID.description',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'string',
      truncate: {
        maxLength: 75,
        isEllipsis: true,
      }
    },
  },
  {
    field: 'expiryDate',
    title: 'DOMAINS.GRID.expiryDate',
    type: 'datetimerange',
    dataSource: '',
    isList: true,
    isViewable: true,
    isSortable: true,
    td: {
      type: 'timestamp',
      nowrap: true
    },
  },
  {
    field: 'createdAt',
    title: 'DOMAINS.GRID.created',
    type: 'datetimerange',
    dataSource: '',
    isList: true,
    isViewable: false,
    isSortable: true,
    td: {
      type: 'timestamp',
      nowrap: true
    },
  },
];
