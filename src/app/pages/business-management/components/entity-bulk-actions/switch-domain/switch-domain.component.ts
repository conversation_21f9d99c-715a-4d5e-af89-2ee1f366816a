import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Domain } from '../../../../../common/models/domain.model';

interface SwitchDomainData {
  componentName: string;
  type: string;
  domains: Domain[];
}

@Component({
  selector: 'switch-domain',
  templateUrl: './switch-domain.component.html',
  styleUrls: ['./switch-domain.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SwitchDomainComponent {
  selectedFromDomain?: Domain;
  selectedToDomain?: Domain;

  domainsList = [];

  constructor( @Inject(MAT_DIALOG_DATA) dialogData: SwitchDomainData,
               public dialogRef: MatDialogRef<SwitchDomainComponent>,
  ) {
    this.domainsList = dialogData?.domains;
  }

  selectFromDomain( domain: Domain | null ) {
    this.selectedFromDomain = domain;
  }

  selectToDomain( domain: Domain | null ) {
    this.selectedToDomain = domain;
  }

  submit() {
    this.dialogRef.close({
      from: this.selectedFromDomain.id,
      to: this.selectedToDomain.id
    });
  }
}
